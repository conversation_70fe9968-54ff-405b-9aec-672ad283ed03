#pragma once

#include "../easy_direct_composition/src/obj_helper.h"

#include <nlohmann/json_fwd.hpp>
#include <string>
#include <curl/curl.h>

class Ollama_API {
    struct Private{};
public:
    Ollama_API(Easy_Object root_object, Private);
    ~Ollama_API();

    static Ollama_API *get_instance();


private:
    void build_common_headers(CURL *curl, std::string path);
    Easy_Object m_root_object;
    struct curl_slist *headers = NULL;

};
