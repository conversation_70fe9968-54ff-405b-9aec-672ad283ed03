#include "gerrit_api.h"
#include <memory>
#include <fstream>
#include <nlohmann/json.hpp>
#include <string>
#include <vector>
#include <codecvt>
#include "../easy_direct_composition/src/dc_env.h"

using json = nlohmann::json;

static std::unique_ptr<Gerrit_API> g_instance;

struct Gerrit_API_Config {
    std::string url;
    std::string username;
    std::string password;
};

namespace nlohmann {
    template <>
    struct adl_serializer<Gerrit_API_Config> {
        static void to_json(json& j, const Gerrit_API_Config& c) {
            j = json{{"url", c.url}, {"username", c.username}, {"password", c.password}};
        }
        static void from_json(const json& j, Gerrit_API_Config& c) {
            c.url = j["url"].get<std::string>();
            c.username = j["username"].get<std::string>();
            c.password = j["password"].get<std::string>();
        }
    };
}

static std::unique_ptr<Gerrit_API_Config> g_config;

std::string Gerrit_API::wstring_to_utf8(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();

    int size_needed = WideCharToMultiByte(
        CP_UTF8, 0, wstr.c_str(), (int)wstr.size(),
        nullptr, 0, nullptr, nullptr
    );
    std::string utf8_str(size_needed, 0);
    WideCharToMultiByte(
        CP_UTF8, 0, wstr.c_str(), (int)wstr.size(),
        &utf8_str[0], size_needed, nullptr, nullptr
    );
    return utf8_str;
}

std::wstring Gerrit_API::utf8_to_wstring(const std::string& str) {
    if (str.empty()) return std::wstring();

    int size_needed = MultiByteToWideChar(
        CP_UTF8, 0, str.c_str(), (int)str.size(),
        nullptr, 0
    );
    std::wstring wstr(size_needed, 0);
    MultiByteToWideChar(
        CP_UTF8, 0, str.c_str(), (int)str.size(),
        &wstr[0], size_needed
    );
    return wstr;
}

std::string Gerrit_API::base64_decode(const std::string &in) {
    std::string out;
    std::vector<int> T(256, -1);
    for (int i = 0; i < 64; i++) T["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[i]] = i;
    int val = 0, valb = -8;
    for (unsigned char c : in) {
        if (c == ' ' || c == '\n' || c == '\r') continue;
        if (T[c] == -1) break;
        val = (val << 6) + T[c];
        valb += 6;
        if (valb >= 0) {
            out.push_back(char((val >> valb) & 0xFF));
            valb -= 8;
        }
    }
    return out;
}

static void load_config()
{
    if (g_config) return;
    std::wstring path = DC_Env::getConfigPath(L"gerrit_client", L"gerrit_api.json");
    std::ifstream f(path);
    if (!f.is_open())
    {
        g_config = std::make_unique<Gerrit_API_Config>();
        DC_Env *env = DC_Env::get_application();
        g_config->url = wstring_to_utf8(env->execDialog(L"Input Gerrit URL", L"Please input the Gerrit URL"));
        g_config->username = wstring_to_utf8(env->execDialog(L"Input Gerrit Username", L"Please input the Gerrit Username"));
        g_config->password = wstring_to_utf8(env->execDialog(L"Input Gerrit Password", L"Please input the Gerrit Password"));
        std::ofstream f(path);
        f << json(*g_config);
        return;
    }
    json j;
    f >> j;
    g_config = std::make_unique<Gerrit_API_Config>(j.get<Gerrit_API_Config>());
}

static size_t WriteCallback(void *contents, size_t size, size_t nmemb, void *userp) {
    ((std::string*)userp)->append((char*)contents, size * nmemb);
    return size * nmemb;
}

Gerrit_API::Gerrit_API(Easy_Object root_object, Private)
    : m_root_object(root_object)
{
    load_config();
    
    headers = curl_slist_append(headers, "Accept: application/json");
    headers = curl_slist_append(headers, "Content-Type: application/json");
}

Gerrit_API::~Gerrit_API()
{
    curl_slist_free_all(headers);
}

Gerrit_API *Gerrit_API::get_instance()
{
    if (!g_instance) {
        g_instance = std::make_unique<Gerrit_API>(Easy_Object::get_root(), Private());
    }
    return g_instance.get();
}

nlohmann::json Gerrit_API::get_changes(const std::string &query)
{
    std::string readBuffer;
    CURL *curl = curl_easy_init();
    build_common_headers(curl, "/changes/?q=" + query);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &readBuffer);
    curl_easy_perform(curl);
    curl_easy_cleanup(curl);
    if (readBuffer.empty() || readBuffer.find(")]}'") == std::string::npos) return json();

    return json::parse(readBuffer.substr(4));
}

void Gerrit_API::build_common_headers(CURL *curl, std::string path)
{
    curl_easy_setopt(curl, CURLOPT_URL, (g_config->url + path).c_str());
    curl_easy_setopt(curl, CURLOPT_USERNAME, g_config->username.c_str());
    curl_easy_setopt(curl, CURLOPT_PASSWORD, g_config->password.c_str());
    curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
}

std::string Gerrit_API::get_patch(const std::string &change_id)
{
    std::string readBuffer;
    CURL *curl = curl_easy_init();
    build_common_headers(curl, "/changes/" + change_id + "/revisions/current/patch");
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &readBuffer);
    curl_easy_perform(curl);
    curl_easy_cleanup(curl);
    return base64_decode(readBuffer);
}