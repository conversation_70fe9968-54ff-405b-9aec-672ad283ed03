#include "ollama_api.h"
#include <memory>
#include <fstream>
#include <nlohmann/json.hpp>
#include <string>
#include <vector>
#include <codecvt>
#include <sstream>
#include <windows.h>
#include <curl/curl.h>
#include "../easy_direct_composition/src/dc_env.h"

using json = nlohmann::json;

static std::unique_ptr<Ollama_API> g_instance;

struct Ollama_API_Config {
    std::string url = "http://localhost:11434";
    std::string api_key = "";
};

namespace nlohmann {
    template <>
    struct adl_serializer<Ollama_API_Config> {
        static void to_json(json& j, const Ollama_API_Config& c) {
            j = json{{"url", c.url}, {"api_key", c.api_key}};
        }
        static void from_json(const json& j, Ollama_API_Config& c) {
            if (j.contains("url")) c.url = j["url"].get<std::string>();
            if (j.contains("api_key")) c.api_key = j["api_key"].get<std::string>();
        }
    };
}

static std::unique_ptr<Ollama_API_Config> g_config;


static void load_config()
{
    if (g_config) return;
    std::wstring path = DC_Env::getConfigPath(L"gerrit_client", L"ollama_api.json");
    std::ifstream f(path);
    if (!f.is_open())
    {
        g_config = std::make_unique<Ollama_API_Config>();
        DC_Env *env = DC_Env::get_application();
        if (env) {
            std::wstring url_input = env->execDialog(L"Input Ollama URL", L"Please input the Ollama URL (default: http://localhost:11434)");
            if (!url_input.empty()) {
                g_config->url = DC_Env::wstring_to_utf8(url_input);
            }
            std::wstring api_key_input = env->execDialog(L"Input Ollama API Key", L"Please input the Ollama API Key (optional)");
            if (!api_key_input.empty()) {
                g_config->api_key = DC_Env::wstring_to_utf8(api_key_input);
            }
        }
        std::ofstream f(path);
        f << json(*g_config);
        return;
    }
    json j;
    f >> j;
    g_config = std::make_unique<Ollama_API_Config>(j.get<Ollama_API_Config>());
}

static size_t WriteCallback(void *contents, size_t size, size_t nmemb, void *userp) {
    ((std::string*)userp)->append((char*)contents, size * nmemb);
    return size * nmemb;
}

static size_t StreamCallback(void *contents, size_t size, size_t nmemb, void *userp) {
    StreamCallback* callback = static_cast<StreamCallback*>(userp);
    std::string chunk((char*)contents, size * nmemb);

    // Parse each line as JSON (streaming format)
    std::istringstream stream(chunk);
    std::string line;
    while (std::getline(stream, line)) {
        if (!line.empty() && line != "\n") {
            try {
                json j = json::parse(line);
                (*callback)(j);
            } catch (const std::exception&) {
                // Ignore malformed JSON lines
            }
        }
    }
    return size * nmemb;
}

Ollama_API::Ollama_API(Easy_Object root_object, Private)
    : m_root_object(root_object)
{
    load_config();

    headers = curl_slist_append(headers, "Accept: application/json");
    headers = curl_slist_append(headers, "Content-Type: application/json");
    if (!g_config->api_key.empty()) {
        std::string auth_header = "Authorization: Bearer " + g_config->api_key;
        headers = curl_slist_append(headers, auth_header.c_str());
    }
}

Ollama_API::~Ollama_API()
{
    curl_slist_free_all(headers);
}

Ollama_API *Ollama_API::get_instance()
{
    if (!g_instance) {
        g_instance = std::make_unique<Ollama_API>(Easy_Object::get_root(), Private());
    }
    return g_instance.get();
}

