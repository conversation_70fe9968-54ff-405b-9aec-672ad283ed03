#include "ollama_api.h"
#include <memory>
#include <fstream>
#include <nlohmann/json.hpp>
#include <string>
#include <vector>
#include <codecvt>
#include <curl/curl.h>
#include "../easy_direct_composition/src/dc_env.h"

using json = nlohmann::json;

static std::unique_ptr<Ollama_API> g_instance;

static size_t WriteCallback(void *contents, size_t size, size_t nmemb, void *userp) {
    ((std::string*)userp)->append((char*)contents, size * nmemb);
    return size * nmemb;
}

Ollama_API::Ollama_API(Easy_Object root_object, Private)
    : m_root_object(root_object)
{
    headers = curl_slist_append(headers, "Accept: application/json");
    headers = curl_slist_append(headers, "Content-Type: application/json");
}

Ollama_API::~Ollama_API()
{
    curl_slist_free_all(headers);
}

Ollama_API *Ollama_API::get_instance()
{
    if (!g_instance) {
        g_instance = std::make_unique<Ollama_API>(Easy_Object::get_root(), Private());
    }
    return g_instance.get();
}

