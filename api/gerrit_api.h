#pragma once

#include "../easy_direct_composition/src/obj_helper.h"

#include <nlohmann/json_fwd.hpp>
#include <string>
#include <curl/curl.h>

class Gerrit_API {
    struct Private{};
public:
    Gerrit_API(Easy_Object root_object, Private);
    ~Gerrit_API();

    static Gerrit_API *get_instance();

    nlohmann::json get_changes(const std::string &query);
    std::string get_patch(const std::string &change_id);

    static std::string base64_decode(const std::string &in);

private:
    void build_common_headers(CURL *curl, std::string path);
    Easy_Object m_root_object;
    struct curl_slist *headers = NULL;

};
