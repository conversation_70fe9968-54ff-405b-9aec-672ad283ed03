
#include <string>
#include <map>
#include <windowsx.h>
#include <windows.h>
#include <commdlg.h>
#include <filesystem>

#include "obj_helper.h"
#include "dc_env.h"
#include "dc_surface.h"
#include "CommCtrl.h"
#include "resource.h"

static const std::map<UINT, std::string> msg_types = {
    {WM_COMMAND, "WM_COMMAND"},
    {WM_RBUTTONUP, "WM_RBUTTONUP"},
    {WM_TIMER, "WM_TIMER"},
    {WM_CLOSE, "WM_CLOSE"},
    {WM_DESTROY, "WM_DESTROY"},
    {WM_MOUSEMOVE, "WM_MOUSEMOVE"}
};


DC_Env* DC_Env::s_application;

DC_Env::DC_Env(HINSTANCE hInstance, Easy_Object root_obj)
    : m_hInstance(hInstance), m_root_obj(root_obj), m_hMainWindow(0)
{
    s_application = this;
}

DC_Env::~DC_Env()
{
    Destroy();
}

int DC_Env::Run()
{
    int result = 0;

    result = EnterMessageLoop();

    Destroy();

    return result;
}

static std::wstring g_input_text;

INT_PTR CALLBACK InputDialogProc(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam)
{
    switch (message)
    {
    case WM_INITDIALOG:
        // Set the default text for the input field
        SetDlgItemText(hDlg, IDC_INPUT_FIELD, L"");
        return TRUE;

    case WM_COMMAND:
        if (LOWORD(wParam) == IDOK)
        {
            // Get the text from the input field
            int textLength = GetWindowTextLength(GetDlgItem(hDlg, IDC_INPUT_FIELD));
            std::wstring text(textLength + 1, L' ');
            GetDlgItemTextW(hDlg, IDC_INPUT_FIELD, &text[0], textLength + 1);
            g_input_text = text;
            EndDialog(hDlg, IDOK);
        }
        else if (LOWORD(wParam) == IDCANCEL)
        {
            EndDialog(hDlg, IDCANCEL);
        }
        break;
    }
    return FALSE;
}

std::wstring DC_Env::execDialog(const std::wstring &title, const std::wstring &message)
{
    g_input_text = L"";
    DialogBoxParamW(m_hInstance, MAKEINTRESOURCE(IDD_INPUT_DIALOG), m_hMainWindow, InputDialogProc, 0);
    return g_input_text;
}

Easy_Object DC_Env::makeVisual(Easy_Object parent_visual)
{
    Easy_Object parent_data = parent_visual.get("data");
    CComPtr<IDCompositionVisual> visual;
    m_pDevice->CreateVisual(&visual);
    Easy_Object visual_obj_data = Easy_Object::pack_COM_object(visual);
    Easy_Object ret = Easy_Object::make_map();
    ret.insert("data", visual_obj_data);
    ret.insert("childs", Easy_Object::make_array());
    if (!parent_data.is_null()) {
        CComPtr<IDCompositionVisual> parent_visual_obj;
        parent_data.get_COM_interface(parent_visual_obj);
        parent_visual_obj->AddVisual(visual, FALSE, NULL);
        Easy_Object parent_childs = parent_visual.get("childs");
        parent_childs.push_back(ret);
    }
    return ret;
}

Easy_Object DC_Env::createSurfaceForVisual(Easy_Object visual, Surface_Geometry geometry)
{
    Easy_Object visual_data = visual.get("data");
    if (visual_data.is_null()) return Easy_Object();
    if (!visual.get("surface").is_null()) return visual.get("surface");
    CComPtr<IDCompositionVisual> visual_obj;
    visual_data.get_COM_interface(visual_obj);
    CComPtr<IDCompositionSurface> surface;
    HRESULT hr = m_pDevice->CreateSurface(geometry.width, geometry.height, DXGI_FORMAT_B8G8R8A8_UNORM, DXGI_ALPHA_MODE_PREMULTIPLIED, &surface);
    if (FAILED(hr)) return Easy_Object();
    visual_obj->SetContent(surface);
    Easy_Object ret = Easy_Object::make_map();
    ret.insert("data", Easy_Object::pack_COM_object(surface));
    visual.insert("surface", ret);
    Easy_Object context_data = Easy_Object::pack_COM_object(m_d2dContext);
    ret.insert("context", context_data);
    visual.insert("geometry", Easy_Object::make_raw(&geometry, sizeof(Surface_Geometry), alignof(Surface_Geometry)));
    return ret;
}

Easy_Object DC_Env::createVisualForSurface(Easy_Object surface, Easy_Object parent_visual)
{
    Easy_Object surface_data = surface.get("data");
    if (surface_data.is_null()) return Easy_Object();
    CComPtr<IDCompositionVisual> visual;
    m_pDevice->CreateVisual(&visual);
    CComPtr<IDCompositionSurface> surface_obj;
    surface_data.get_COM_interface(surface_obj);
    visual->SetContent(surface_obj);
    Easy_Object visual_obj_data = Easy_Object::pack_COM_object(visual);
    Easy_Object ret = Easy_Object::make_map();
    ret.insert("data", visual_obj_data);
    if (!parent_visual.is_null()) {
        Easy_Object parent_data = parent_visual.get("data");
        if (!parent_data.is_null()) {
            CComPtr<IDCompositionVisual> parent_visual_obj;
            parent_data.get_COM_interface(parent_visual_obj);
            parent_visual_obj->AddVisual(visual, FALSE, NULL);
            Easy_Object parent_childs = parent_visual.get("childs");
            parent_childs.push_back(ret);
        }
    }
    return ret;
}

Easy_Object DC_Env::createSurface(int width, int height)
{
    CComPtr<IDCompositionSurface> surface;
    HRESULT hr = m_pDevice->CreateSurface(width, height, DXGI_FORMAT_B8G8R8A8_UNORM, DXGI_ALPHA_MODE_PREMULTIPLIED, &surface);
    if (FAILED(hr)) return Easy_Object();
    Easy_Object ret = Easy_Object::make_map();
    ret.insert("data", Easy_Object::pack_COM_object(surface));
    return ret;
}

Easy_Object DC_Env::createSwapChainSurfaceForVisual(Easy_Object visual, Surface_Geometry geometry, UINT buffer_count)
{
    Easy_Object visual_data = visual.get("data");
    if (visual_data.is_null()) return Easy_Object();
    if (!visual.get("surface").is_null()) return visual.get("surface");

    CComPtr<IDCompositionVisual> visual_obj;
    visual_data.get_COM_interface(visual_obj);

    // Create swap chain description
    DXGI_SWAP_CHAIN_DESC1 swapChainDesc = {};
    swapChainDesc.Width = geometry.width;
    swapChainDesc.Height = geometry.height;
    swapChainDesc.Format = DXGI_FORMAT_B8G8R8A8_UNORM;
    swapChainDesc.Stereo = FALSE;
    swapChainDesc.SampleDesc.Count = 1;
    swapChainDesc.SampleDesc.Quality = 0;
    swapChainDesc.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    swapChainDesc.BufferCount = buffer_count;
    swapChainDesc.Scaling = DXGI_SCALING_STRETCH;
    swapChainDesc.SwapEffect = DXGI_SWAP_EFFECT_FLIP_SEQUENTIAL;
    swapChainDesc.AlphaMode = DXGI_ALPHA_MODE_PREMULTIPLIED;
    swapChainDesc.Flags = 0;

    // Create swap chain
    CComPtr<IDXGISwapChain1> swapChain;
    HRESULT hr = m_dxgiFactory->CreateSwapChainForComposition(_d3d11Device, &swapChainDesc, nullptr, &swapChain);
    if (FAILED(hr)) return Easy_Object();

    // Set content to visual
    visual_obj->SetContent(swapChain);

    // Create return object
    Easy_Object ret = Easy_Object::make_map();
    //ret.insert("data", Easy_Object::pack_COM_object(surface));
    ret.insert("swap_chain", Easy_Object::pack_COM_object(swapChain));
    ret.insert("type", Easy_Object::make_char32_string(U"swap_chain_surface"));
    visual.insert("surface", ret);

    Easy_Object context_data = Easy_Object::pack_COM_object(m_d2dContext);
    ret.insert("context", context_data);
    visual.insert("geometry", Easy_Object::make_raw(&geometry, sizeof(Surface_Geometry), alignof(Surface_Geometry)));

    return ret;
}

Easy_Object DC_Env::createChildWindowSurfaceForVisual(Easy_Object visual, Surface_Geometry geometry, HWND window, HWND parent_window)
{
    Easy_Object visual_obj = visual.get("data");
    if (visual_obj.is_null()) return Easy_Object();
    if (!visual.get("surface").is_null()) return Easy_Object();

    CComPtr<IDCompositionVisual> visual_data;
    visual_obj.get_COM_interface(visual_data);
    CComPtr<IUnknown> surface;
    HWND warpper_window = NULL;

    // Check if the parent window has WS_EX_NOREDIRECTIONBITMAP style
    if (parent_window) {
        LONG_PTR exStyle = GetWindowLongPtr(parent_window, GWL_EXSTYLE);
        if (exStyle & WS_EX_NOREDIRECTIONBITMAP) {
            // If so, create a layered window wrapper
            warpper_window = createLayeredWindowWrapper(window, geometry.x, geometry.y, geometry.width, geometry.height);
            window = warpper_window;
        }
    }

    HRESULT hr = m_pDevice->CreateSurfaceFromHwnd(window, &surface);

    if (FAILED(hr))
    {
        
        if (warpper_window)
        {
            DestroyWindow(warpper_window);
        }
        return {};
    }

    visual_data->SetContent(surface);

    Easy_Object ret = Easy_Object::make_map();
    ret.insert("data", Easy_Object::pack_COM_object(surface));
    ret.insert("type", Easy_Object::make_char32_string(U"child_window_surface"));
    if (warpper_window) {
        ret.insert("wrapper_window", Easy_Object::make_HWND_object(warpper_window));
    }
    visual.insert("surface", ret);

    return ret;
}

void DC_Env::moveVisual(Easy_Object visual, float x, float y)
{
    Easy_Object visual_data = visual.get("data");
    if (visual_data.is_null()) return;
    CComPtr<IDCompositionVisual> visual_obj;
    visual_data.get_COM_interface(visual_obj);
    visual_obj->SetOffsetX(x);
    visual_obj->SetOffsetY(y);
    Easy_Object geometry = visual.get("geometry");
    if (!geometry.is_null()) {
        Surface_Geometry *geo = (Surface_Geometry*)geometry.get_data_ptr();
        geo->x = x;
        geo->y = y;
    }
}

void DC_Env::setVisualClip(Easy_Object visual, Surface_Geometry geometry)
{
    Easy_Object visual_data = visual.get("data");
    if (visual_data.is_null()) return;
    CComPtr<IDCompositionVisual> visual_obj;
    visual_data.get_COM_interface(visual_obj);
    D2D_RECT_F rect = D2D1::RectF(geometry.x, geometry.y, geometry.x + geometry.width, geometry.y + geometry.height);
    visual_obj->SetClip(rect);
}

void DC_Env::removeVisualClip(Easy_Object visual)
{
    Easy_Object visual_data = visual.get("data");
    if (visual_data.is_null()) return;
    CComPtr<IDCompositionVisual> visual_obj;
    visual_data.get_COM_interface(visual_obj);
    visual_obj->SetClip(NULL);
}

int DC_Env::EnterMessageLoop()
{
    int result = 0;

    MSG msg = { 0 };

    while (GetMessage(&msg, NULL, 0, 0))
    {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    result = static_cast<int>(msg.wParam);

    return result;
}

int DC_Env::RenderLoop(HANDLE hQuit, HANDLE hFrameLatencyWait)
{

    return 0;
}


HRESULT DC_Env::Initialize()
{
    InitCommonControls();
    HRESULT hr = InitializeMainWindow();

    if (SUCCEEDED(hr))
    {
        CreateObjectTree();
    }

    if (SUCCEEDED(hr))
    {
        hr = CreateD3D11Device();
    }

    if (SUCCEEDED(hr))
    {
        hr = CreateDCompositionDevice();
    }

    if (SUCCEEDED(hr))
    {
        hr = CreateDCompositionRenderTarget();
    }

    if (SUCCEEDED(hr))
    {
        hr = CreateDCompositionVisualTree();
    }

    if (SUCCEEDED(hr))
    {
        // Commit the batch.
        hr = m_pDevice->Commit();
    }

    return hr;

}

void DC_Env::CreateObjectTree()
{
    m_dc_obj = Easy_Object::make_map();
    m_root_obj.insert("DirectComposition", m_dc_obj);
    m_dc_obj.insert("event_handlers", Easy_Object::make_map());
    Easy_Object::type_register<std::mutex>(U"std::mutex");

}

bool DC_Env::registerEventHandler(UINT msg_type, Easy_Object handler)
{
    std::string msg_name = msg_types.find(msg_type) != msg_types.end() ? msg_types.at(msg_type) : std::string();
    if (msg_name.size()) {
        m_dc_obj.get("event_handlers").insert(msg_name, handler);
        return true;
    }
    return false;
}

void DC_Env::markSurfaceDirty(Easy_Object surface_obj)
{
    std::lock_guard<std::mutex> lock(m_dirty_surfaces_mutex);
    m_dirty_surfaces.insert(surface_obj.get_ptr());
}

HRESULT DC_Env::presentSwapChain(Easy_Object swap_chain_surface)
{
    Easy_Object swap_chain_obj = swap_chain_surface.get("swap_chain");
    if (swap_chain_obj.is_null()) return E_INVALIDARG;

    CComPtr<IDXGISwapChain1> swapChain;
    swap_chain_obj.get_COM_interface(swapChain);

    return swapChain->Present(1, 0);
}

HRESULT DC_Env::resizeSwapChainBuffers(Easy_Object swap_chain_surface, UINT width, UINT height)
{
    Easy_Object swap_chain_obj = swap_chain_surface.get("swap_chain");
    if (swap_chain_obj.is_null()) return E_INVALIDARG;

    CComPtr<IDXGISwapChain1> swapChain;
    swap_chain_obj.get_COM_interface(swapChain);

    // Get current swap chain description
    DXGI_SWAP_CHAIN_DESC1 desc;
    HRESULT hr = swapChain->GetDesc1(&desc);
    if (FAILED(hr)) return hr;

    // Resize buffers
    hr = swapChain->ResizeBuffers(desc.BufferCount, width, height, desc.Format, desc.Flags);
    if (FAILED(hr)) return hr;

    // Update geometry
    Easy_Object geometry_obj = swap_chain_surface.get("geometry");
    if (!geometry_obj.is_null()) {
        Surface_Geometry* geometry = (Surface_Geometry*)geometry_obj.get_data_ptr();
        geometry->width = width;
        geometry->height = height;
    }

    return S_OK;
}

// Creates the main application window.
HRESULT DC_Env::InitializeMainWindow()
{
    HRESULT hr = S_OK;

    // Register the window class.
    WNDCLASSEXW wc     = {0};
    wc.cbSize         = sizeof(wc);
    wc.style          = CS_HREDRAW | CS_VREDRAW;
    wc.lpfnWndProc    = WindowProc;
    wc.hInstance      = m_hInstance;
    wc.hCursor        = LoadCursor(NULL, IDC_ARROW);
    wc.hbrBackground  = static_cast<HBRUSH>(GetStockObject(BLACK_BRUSH));
    wc.lpszClassName  = L"GerritClientWindowClass";

    RegisterClassExW(&wc);

    // Creates the m_hMainWindow window.
    m_hMainWindow = CreateWindowExW(WS_EX_OVERLAPPEDWINDOW | WS_EX_NOREDIRECTIONBITMAP,                          // Extended window style
                                   wc.lpszClassName,                                // Name of window class
                                   L"Client", // Title-bar string
                                   WS_OVERLAPPED | WS_SYSMENU,                      // Top-level window
                                   CW_USEDEFAULT,                                   // Horizontal position
                                   CW_USEDEFAULT,                                   // Vertical position
                                   1000,                                            // Width
                                   700,                                             // Height
                                   NULL,                                            // Parent
                                   NULL,                                            // Class menu
                                   GetModuleHandle(NULL),                           // Handle to application instance
                                   NULL                                             // Window-creation data
                                   );

    if (!m_hMainWindow)
    {
        hr = HRESULT_FROM_WIN32(GetLastError());
    }

    if (SUCCEEDED(hr))
    {
        ShowWindow(m_hMainWindow, SW_SHOWDEFAULT);
        wchar_t buffer[256];
        GetWindowTextW(m_hMainWindow, buffer, 256);
        OutputDebugStringW(L"Current window title: ");
        OutputDebugStringW(buffer);
        OutputDebugStringW(L"\n");
    }

    return hr;
}

HRESULT DC_Env::CreateD3D11Device()
{
    HRESULT hr = S_OK;

    D3D_DRIVER_TYPE driverTypes[] =
    {
        D3D_DRIVER_TYPE_HARDWARE,
        D3D_DRIVER_TYPE_WARP,
    };

    D3D_FEATURE_LEVEL featureLevelSupported;

    for (int i = 0; i < sizeof(driverTypes) / sizeof(driverTypes[0]); ++i)
    {
        CComPtr<ID3D11Device> d3d11Device;
        CComPtr<ID3D11DeviceContext> d3d11DeviceContext;

        hr = D3D11CreateDevice(
            nullptr,
            driverTypes[i],
            NULL,
            D3D11_CREATE_DEVICE_BGRA_SUPPORT,
            NULL,
            0,
            D3D11_SDK_VERSION,
            &d3d11Device,
            &featureLevelSupported,
            &d3d11DeviceContext);

        if (SUCCEEDED(hr))
        {
            _d3d11Device = d3d11Device.Detach();
            _d3d11DeviceContext = d3d11DeviceContext.Detach();

            break;
        }
    }
    if (SUCCEEDED(hr))
    {
        D2D1_FACTORY_OPTIONS options = {};
        hr = D2D1CreateFactory(D2D1_FACTORY_TYPE_SINGLE_THREADED, __uuidof(ID2D1Factory), &options, reinterpret_cast<void**>(&m_d2dFactory));
    }

    if (SUCCEEDED(hr))
    {
        hr = _d3d11Device->QueryInterface(&m_dxgiDevice);
    }

    if (SUCCEEDED(hr))
    {
        CComPtr<IDXGIAdapter> adapter;
        hr = m_dxgiDevice->GetAdapter(&adapter);
        if (SUCCEEDED(hr))
        {
            hr = adapter->GetParent(__uuidof(IDXGIFactory2), reinterpret_cast<void**>(&m_dxgiFactory));
        }
    }

    if (SUCCEEDED(hr))
    {
        hr = D2D1CreateDevice(m_dxgiDevice, NULL, &m_d2dDevice);
    }

    if (SUCCEEDED(hr))
    {
        hr = m_d2dDevice->CreateDeviceContext(D2D1_DEVICE_CONTEXT_OPTIONS_NONE, &m_d2dContext);
    }

    if (SUCCEEDED(hr))
    {
        hr = DWriteCreateFactory(DWRITE_FACTORY_TYPE_SHARED, __uuidof(IDWriteFactory), reinterpret_cast<IUnknown**>(&m_dwriteFactory));
    }

    return hr;
}

HRESULT DC_Env::CreateDCompositionDevice()
{
    HRESULT hr = (_d3d11Device == nullptr) ? E_UNEXPECTED : S_OK;

    if (SUCCEEDED(hr))
    {
        hr = DCompositionCreateDevice(m_dxgiDevice, __uuidof(IDCompositionDevice), reinterpret_cast<void **>(&m_pDevice));
    }

    return hr;
}

HRESULT DC_Env::CreateDCompositionRenderTarget()
{
    HRESULT hr = ((m_pDevice == nullptr) || (m_hMainWindow == NULL)) ? E_UNEXPECTED : S_OK;

    if (SUCCEEDED(hr))
    {
        // FALSE puts the composition content beneath the Win32 buttons.
        hr = m_pDevice->CreateTargetForHwnd(m_hMainWindow, FALSE, &m_pHwndRenderTarget);
    }

    return hr;
}

HRESULT DC_Env::CreateDCompositionVisualTree()
{
    HRESULT hr = ((m_pDevice == nullptr) || (m_hMainWindow == NULL)) ? E_UNEXPECTED : S_OK;

    if (SUCCEEDED(hr))
    {
        // Create the root visual.
        hr = m_pDevice->CreateVisual(&m_pRootVisual);
    }

    if (SUCCEEDED(hr))
    {
        // Make the visual the root of the tree.
        hr = m_pHwndRenderTarget->SetRoot(m_pRootVisual);
    }

    if (SUCCEEDED(hr))
    {
        Easy_Object root_visual = Easy_Object::make_map();
        root_visual.insert("data", Easy_Object::pack_COM_object(m_pRootVisual));
        root_visual.insert("childs", Easy_Object::make_array());
        m_dc_obj.insert("root_visual", root_visual);
    }

    return hr;
}

//------------------------------------------------------
// In Action
//------------------------------------------------------

// Main window procedure
LRESULT CALLBACK DC_Env::WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    LRESULT result = 0;
    std::string msg_type = msg_types.find(uMsg) != msg_types.end() ? msg_types.at(uMsg) : std::string();
    if (msg_type.size()) {
        result = s_application->ProcessMessage(msg_type, hwnd, uMsg, wParam, lParam);
    }

    switch (uMsg)
    {
        case WM_COMMAND:
            break;

        case WM_RBUTTONUP:
            break;

        case WM_TIMER:
            break;

        case WM_CLOSE:
            result = s_application->OnClose(hwnd);
            break;

        case WM_DESTROY:
            result = s_application->OnDestroy(hwnd);
            break;

        case WM_MOUSEMOVE:
            break;

        default:
            result = DefWindowProc(hwnd, uMsg, wParam, lParam);
    }

    return result;
}

LRESULT DC_Env::ProcessMessage(const std::string &msg_type, HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    Easy_Object event_handlers = m_dc_obj.get("event_handlers");
    Easy_Object handler = event_handlers.get(msg_type);
    if (handler.is_null()) return 0;
    Message_Handler_Closure *closure_data = (Message_Handler_Closure*)handler.get_data_ptr();
    Message_Handler func = closure_data->func;
    func(handler, hwnd, uMsg, wParam, lParam);
    return 0;
}

// Handles the WM_CLOSE message.
LRESULT DC_Env::OnClose(HWND /*hwnd*/)
{
    // Destroy the main window.
    DestroyWindow(m_hMainWindow);

    return 0;
}

// Handles the WM_DESTROY message.
LRESULT DC_Env::OnDestroy(HWND /*hwnd*/)
{
    PostQuitMessage(0);

    return 0;
}


//------------------------------------------------------
// Destroy
//------------------------------------------------------

VOID DC_Env::Destroy()
{
    DestroyMainWindow();
    DestroyDCompositionVisualTree();
    DestroyDCompositionRenderTarget();
    DestroyDCompositionDevice();
    DestroyD3D11Device();
    CoUninitialize();
}

VOID DC_Env::DestroyMainWindow()
{
    if (m_hMainWindow != NULL)
    {
       DestroyWindow(m_hMainWindow);
       m_hMainWindow = NULL;
    }
}


VOID DC_Env::DestroyDCompositionVisualTree()
{
}

VOID DC_Env::DestroyDCompositionRenderTarget()
{
    m_pHwndRenderTarget = nullptr;
}

VOID DC_Env::DestroyDCompositionDevice()
{
    m_pDevice = nullptr;
}

VOID DC_Env::DestroyD3D11Device()
{
    m_d2dContext = nullptr;
    m_d2dDevice = nullptr;
    m_d2dFactory = nullptr;
    m_dxgiFactory = nullptr;
    m_dxgiDevice = nullptr;
    m_dwriteFactory = nullptr;
    _d3d11DeviceContext = nullptr;
    _d3d11Device = nullptr;
}

// 添加一个创建分层窗口包装器的方法
HWND DC_Env::createLayeredWindowWrapper(HWND controlHwnd, int x, int y, int width, int height)
{
    // 注册窗口类（如果尚未注册）
    static bool classRegistered = false;
    if (!classRegistered) {
        WNDCLASSEXW wcex = {0};
        wcex.cbSize = sizeof(WNDCLASSEX);
        wcex.style = CS_HREDRAW | CS_VREDRAW;
        wcex.lpfnWndProc = DefWindowProc;
        wcex.hInstance = m_hInstance;
        wcex.hCursor = LoadCursor(NULL, IDC_ARROW);
        wcex.lpszClassName = L"LayeredWrapperClass";
        wcex.hbrBackground = static_cast<HBRUSH>(GetStockObject(BLACK_BRUSH));
        RegisterClassExW(&wcex);
        classRegistered = true;
    }
    
    // 创建分层窗口
    HWND layeredHwnd = CreateWindowExW(
        WS_EX_LAYERED,  // 使用分层窗口
        L"LayeredWrapperClass",
        L"",
        WS_CHILD | WS_CLIPSIBLINGS,
        x, y, width, height,
        m_hMainWindow,  // 父窗口是主窗口
        NULL,
        m_hInstance,
        NULL);
       
    HRESULT hr = HRESULT_FROM_WIN32(GetLastError());
    if (layeredHwnd) {
        // 设置分层窗口属性
        SetLayeredWindowAttributes(layeredHwnd, 0, 255, LWA_ALPHA);
        
        // 将控件重设为分层窗口的子窗口
        SetParent(controlHwnd, layeredHwnd);
        SetWindowPos(controlHwnd, NULL, 0, 0, width, height, 
                    SWP_NOZORDER | SWP_SHOWWINDOW);
        ShowWindow(layeredHwnd, SW_SHOWDEFAULT);
    }
    
    return layeredHwnd;
}

std::wstring DC_Env::getConfigPath(std::wstring module_name, std::wstring config_name)
{
    namespace fs = std::filesystem;
    wchar_t appDataPath[MAX_PATH];
    ExpandEnvironmentStringsW(L"%APPDATA%", appDataPath, MAX_PATH);

    fs::path path = appDataPath;
    path /= module_name;
    fs::create_directories(path);  // 自动创建多级目录

    path /= config_name;
    return path.wstring();
}
